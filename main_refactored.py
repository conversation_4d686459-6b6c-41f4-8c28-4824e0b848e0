"""
Refactored main.py using unified modules for clean and consistent code.
This demonstrates the improved architecture with:
- Unified data processing with feature engineering
- Model factory for consistent model creation
- Unified trainer for all model types
- Comprehensive evaluation with all metrics
- Clean separation of concerns
"""

from arguments import parse_args
from data_processing import DataProcessor
from model_factory import ModelFactory
from trainer import UnifiedTrainer
from evaluator import ModelEvaluator
from plotting import plot_losses, plot_test_acc, plot_enhanced_metrics
import torch
import pandas as pd
import numpy as np
import random


def set_random_seeds(seed=42):
    """Set random seeds for reproducibility across all libraries."""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    # For deterministic behavior (may impact performance)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    print(f"Random seeds set to {seed} for reproducibility")


def setup_device(args):
    """Setup device with proper GPU management."""
    if args.force_cpu:
        device = 'cpu'
        print("Forced CPU usage")
    elif args.device == 'cpu':
        device = 'cpu'
        print("Using CPU")
    elif args.device == 'cuda':
        if torch.cuda.is_available():
            device = f'cuda:{args.gpu_id}'
            torch.cuda.set_device(args.gpu_id)
            print(f"Using GPU {args.gpu_id}: {torch.cuda.get_device_name(args.gpu_id)}")
        else:
            device = 'cpu'
            print("CUDA not available, falling back to CPU")
    else:  # auto
        if torch.cuda.is_available() and not args.force_cpu:
            device = f'cuda:{args.gpu_id}'
            torch.cuda.set_device(args.gpu_id)
            print(f"Auto-selected GPU {args.gpu_id}: {torch.cuda.get_device_name(args.gpu_id)}")
        else:
            device = 'cpu'
            print("Auto-selected CPU")

    # Clear CUDA cache if requested
    if args.clear_cuda_cache and torch.cuda.is_available():
        torch.cuda.empty_cache()
        print("CUDA cache cleared")

    return device


def main():
    """Main function with clean, unified approach."""
    # Setup
    args = parse_args()
    device = setup_device(args)

    # Set random seeds for reproducibility
    if hasattr(args, 'random_seed'):
        set_random_seeds(args.random_seed)

    print(f"Training {args.model_name} model")
    print(f"Device: {device}")
    print(f"Test fold: {args.test_fold}/{args.kfold}")
    print(f"Feature engineering: {args.apply_feature_engineering}")
    print(f"Feature scaling: {args.apply_scaling}")
    print(f"Data shuffling: {getattr(args, 'shuffle_data', False)}")
    print(f"Random seed: {getattr(args, 'random_seed', 42)}")

    # Data processing
    print("\nProcessing data...")

    # For sklearn models, we don't normalize labels as they handle it internally
    # Hybrid models also don't need label scaling since they use sklearn components
    apply_label_scaling = ModelFactory.is_pytorch_model(args.model_name)

    data_processor = DataProcessor(
        apply_feature_engineering=args.apply_feature_engineering,
        apply_feature_scaling=args.apply_scaling,
        apply_label_scaling=apply_label_scaling  # Only scale labels for PyTorch models
    )

    # Determine data format based on model type
    if ModelFactory.is_pytorch_model(args.model_name):
        train_data, test_data = data_processor.process_data(args, return_format='dataloader')
        print(f"Created PyTorch DataLoaders")
    elif ModelFactory.is_hybrid_model(args.model_name):
        # Hybrid models need numpy data for sklearn components
        X_train, y_train, X_test, y_test = data_processor.process_data(args, return_format='numpy')
        train_data = (X_train, y_train)
        test_data = (X_test, y_test)
        print(f"Created numpy arrays for hybrid model: X_train {X_train.shape}, y_train {y_train.shape}")
    else:
        X_train, y_train, X_test, y_test = data_processor.process_data(args, return_format='numpy')
        train_data = (X_train, y_train)
        test_data = (X_test, y_test)
        print(f"Created numpy arrays: X_train {X_train.shape}, y_train {y_train.shape}")

    # Model creation
    print(f"\nCreating {args.model_name} model...")
    model = ModelFactory.create_model(args.model_name, args, device)
    print(f"Model created successfully")

    # Training
    print(f"\nTraining {args.model_name} model...")
    trainer = UnifiedTrainer(model, args.model_name, args, device)
    train_losses, test_losses, test_acc, enhanced_metrics = trainer.train(train_data, test_data)

    # Evaluation summary
    evaluator = ModelEvaluator()
    evaluator.print_metrics(enhanced_metrics, args.model_name)

    # Determine epochs for plotting
    epochs = len(train_losses) if len(train_losses) > 1 else 1

    # Plotting
    print(f"\nGenerating plots...")
    plot_losses(train_losses, test_losses, epochs, args)
    plot_test_acc(test_acc, epochs, args)
    plot_enhanced_metrics(enhanced_metrics, args)

    # Save results
    print(f"\nSaving results...")
    save_results(train_losses, test_losses, test_acc, enhanced_metrics, args)

    print(f"\nTraining completed successfully!")
    print(f"Results saved to: ./res/{args.model_name}-{args.test_fold}.csv")
    print(f"Plots saved to: ./plots/")


def save_results(train_losses, test_losses, test_acc, enhanced_metrics, args):
    """Save results in the same format as the original main.py."""
    res = []
    res.append(train_losses)
    res.append(test_losses)
    res.append(test_acc[0])  # power accuracy
    res.append(test_acc[1])  # area accuracy
    res.append([enhanced_metrics.get('power_mse', 0)])
    res.append([enhanced_metrics.get('area_mse', 0)])
    res.append([enhanced_metrics.get('overall_mse', 0)])
    res.append([enhanced_metrics.get('power_r2', 0)])
    res.append([enhanced_metrics.get('area_r2', 0)])
    res.append([enhanced_metrics.get('overall_r2', 0)])
    res.append([enhanced_metrics.get('power_rmse', 0)])
    res.append([enhanced_metrics.get('area_rmse', 0)])
    res.append([enhanced_metrics.get('overall_rmse', 0)])
    res.append([enhanced_metrics.get('power_mape', 0)])
    res.append([enhanced_metrics.get('area_mape', 0)])
    res.append([enhanced_metrics.get('overall_mape', 0)])

    index_labels = [
        'train losses', 'test losses', 'power acc', 'area acc',
        'power mse', 'area mse', 'overall mse',
        'power r2', 'area r2', 'overall r2',
        'power rmse', 'area rmse', 'overall rmse',
        'power mape', 'area mape', 'overall mape'
    ]

    pd.DataFrame(res, index=index_labels).to_csv(f'./res/{args.model_name}-{args.test_fold}.csv')


def run_cross_validation(model_name, args_override=None):
    """
    Run 5-fold cross-validation for a given model.

    Args:
        model_name: Name of the model to test
        args_override: Dictionary of argument overrides
    """
    print(f"\n{'='*60}")
    print(f"RUNNING 5-FOLD CROSS-VALIDATION FOR {model_name.upper()}")
    print('='*60)

    results = []

    for fold in range(5):
        print(f"\nFold {fold + 1}/5")
        print("-" * 30)

        # Parse arguments and override model name and fold
        args = parse_args()
        args.model_name = model_name
        args.test_fold = fold

        # Apply any additional overrides
        if args_override:
            for key, value in args_override.items():
                setattr(args, key, value)

        # Set random seeds for reproducibility
        if hasattr(args, 'random_seed'):
            set_random_seeds(args.random_seed)

        # Run training for this fold
        device = setup_device(args)

        # Data processing
        # For sklearn models, we don't normalize labels as they handle it internally
        # Hybrid models also don't need label scaling since they use sklearn components
        apply_label_scaling = ModelFactory.is_pytorch_model(args.model_name)

        print(f"DEBUG: args.apply_scaling = {args.apply_scaling}")
        print(f"DEBUG: args.apply_feature_engineering = {args.apply_feature_engineering}")
        print(f"DEBUG: apply_label_scaling = {apply_label_scaling}")

        data_processor = DataProcessor(
            apply_feature_engineering=args.apply_feature_engineering,
            apply_feature_scaling=args.apply_scaling,  # args.apply_scaling maps to apply_feature_scaling parameter
            apply_label_scaling=apply_label_scaling
        )

        if ModelFactory.is_pytorch_model(args.model_name):
            train_data, test_data = data_processor.process_data(args, return_format='dataloader')
        elif ModelFactory.is_hybrid_model(args.model_name):
            X_train, y_train, X_test, y_test = data_processor.process_data(args, return_format='numpy')
            train_data = (X_train, y_train)
            test_data = (X_test, y_test)
        else:
            X_train, y_train, X_test, y_test = data_processor.process_data(args, return_format='numpy')
            train_data = (X_train, y_train)
            test_data = (X_test, y_test)

        # Model creation and training
        model = ModelFactory.create_model(args.model_name, args, device)
        trainer = UnifiedTrainer(model, args.model_name, args, device)
        _, _, _, enhanced_metrics = trainer.train(train_data, test_data)

        results.append(enhanced_metrics)

        print(f"Fold {fold + 1} - R²: {enhanced_metrics['overall_r2']:.4f}, "
              f"MSE: {enhanced_metrics['overall_mse']:.6f}, "
              f"Power Acc: {enhanced_metrics['power_accuracy']:.4f}, "
              f"Area Acc: {enhanced_metrics['area_accuracy']:.4f}")

    # Calculate average metrics
    avg_metrics = {}
    for key in results[0].keys():
        if isinstance(results[0][key], (int, float)):
            avg_metrics[key] = sum(result[key] for result in results) / len(results)

    print(f"\n{'='*60}")
    print(f"CROSS-VALIDATION SUMMARY FOR {model_name.upper()}")
    print('='*60)

    print(f"\nR² Metrics:")
    print(f"  Power R²:   {avg_metrics['power_r2']:.4f}")
    print(f"  Area R²:    {avg_metrics['area_r2']:.4f}")
    print(f"  Overall R²: {avg_metrics['overall_r2']:.4f}")

    print(f"\nMSE Metrics:")
    print(f"  Power MSE:   {avg_metrics['power_mse']:.6f}")
    print(f"  Area MSE:    {avg_metrics['area_mse']:.6f}")
    print(f"  Overall MSE: {avg_metrics['overall_mse']:.6f}")

    print(f"\nRMSE Metrics:")
    print(f"  Power RMSE:   {avg_metrics['power_rmse']:.6f}")
    print(f"  Area RMSE:    {avg_metrics['area_rmse']:.6f}")
    print(f"  Overall RMSE: {avg_metrics['overall_rmse']:.6f}")

    print(f"\nMAPE Metrics:")
    print(f"  Power MAPE:   {avg_metrics['power_mape']:.4f}")
    print(f"  Area MAPE:    {avg_metrics['area_mape']:.4f}")
    print(f"  Overall MAPE: {avg_metrics['overall_mape']:.4f}")

    print(f"\nAccuracy Metrics:")
    print(f"  Power Accuracy: {avg_metrics['power_accuracy']:.4f}")
    print(f"  Area Accuracy:  {avg_metrics['area_accuracy']:.4f}")

    print('='*60)

    return results, avg_metrics


def run_cross_validation_main():
    """Main function that runs cross-validation by default."""
    args = parse_args()

    # Add option to choose between single fold and cross-validation
    import sys
    if '--single-fold' in sys.argv:
        print("Running single fold evaluation...")
        main()
    else:
        print("Running k-fold cross-validation...")
        results, avg_metrics = run_cross_validation(args.model_name)

        # Save averaged results
        save_cross_validation_results(results, avg_metrics, args)

        print(f"\nCross-validation completed!")
        print(f"Averaged results saved to: ./res/{args.model_name}-cv-average.csv")
        print(f"Individual fold results saved to: ./res/{args.model_name}-cv-all-folds.csv")


def save_cross_validation_results(results, avg_metrics, args):
    """Save cross-validation results."""
    # Save averaged results
    avg_res = []
    avg_res.append([avg_metrics.get('overall_mse', 0)])  # Use MSE as loss
    avg_res.append([avg_metrics.get('overall_mse', 0)])  # Test loss same as MSE
    avg_res.append([avg_metrics.get('power_accuracy', 0)])  # Power accuracy
    avg_res.append([avg_metrics.get('area_accuracy', 0)])   # Area accuracy
    avg_res.append([avg_metrics.get('power_mse', 0)])
    avg_res.append([avg_metrics.get('area_mse', 0)])
    avg_res.append([avg_metrics.get('overall_mse', 0)])
    avg_res.append([avg_metrics.get('power_r2', 0)])
    avg_res.append([avg_metrics.get('area_r2', 0)])
    avg_res.append([avg_metrics.get('overall_r2', 0)])
    avg_res.append([avg_metrics.get('power_rmse', 0)])
    avg_res.append([avg_metrics.get('area_rmse', 0)])
    avg_res.append([avg_metrics.get('overall_rmse', 0)])
    avg_res.append([avg_metrics.get('power_mape', 0)])
    avg_res.append([avg_metrics.get('area_mape', 0)])
    avg_res.append([avg_metrics.get('overall_mape', 0)])

    avg_index_labels = [
        'train losses', 'test losses', 'power acc', 'area acc',
        'power mse', 'area mse', 'overall mse',
        'power r2', 'area r2', 'overall r2',
        'power rmse', 'area rmse', 'overall rmse',
        'power mape', 'area mape', 'overall mape'
    ]

    # Save averaged results
    pd.DataFrame(avg_res, index=avg_index_labels).to_csv(f'./res/{args.model_name}-cv-average.csv')

    # Save all individual fold results
    all_folds_data = []
    for i, fold_result in enumerate(results):
        fold_data = [
            fold_result.get('overall_mse', 0),  # train loss
            fold_result.get('overall_mse', 0),  # test loss
            fold_result.get('power_accuracy', 0),
            fold_result.get('area_accuracy', 0),
            fold_result.get('power_mse', 0),
            fold_result.get('area_mse', 0),
            fold_result.get('overall_mse', 0),
            fold_result.get('power_r2', 0),
            fold_result.get('area_r2', 0),
            fold_result.get('overall_r2', 0),
            fold_result.get('power_rmse', 0),
            fold_result.get('area_rmse', 0),
            fold_result.get('overall_rmse', 0),
            fold_result.get('power_mape', 0),
            fold_result.get('area_mape', 0),
            fold_result.get('overall_mape', 0)
        ]
        all_folds_data.append(fold_data)

    # Create DataFrame with fold columns
    fold_columns = [f'fold_{i}' for i in range(len(results))]
    pd.DataFrame(all_folds_data, columns=avg_index_labels, index=fold_columns).T.to_csv(f'./res/{args.model_name}-cv-all-folds.csv')


if __name__ == '__main__':
    run_cross_validation_main()
