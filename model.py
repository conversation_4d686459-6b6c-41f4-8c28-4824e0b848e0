import torch
import torch.nn as nn
from arguments import get_args
import math

class MLP(nn.Module):
    def __init__(self, num_embeddings=147, embed_dim=128, out_dim=2) -> None:
        super().__init__()
        self.embed_dim = embed_dim
        self.f = 29             # feature num

        self.activation = nn.ReLU()

        # Use direct linear layers instead of embedding table for continuous features
        self.linear_relu_stack = nn.Sequential(
            nn.Linear(self.f, embed_dim),
            self.activation,
            nn.Dropout(0.2),

            nn.Linear(embed_dim, embed_dim),
            self.activation,
            nn.Dropout(0.2),

            nn.Linear(embed_dim, embed_dim // 2),
            self.activation,

            nn.Linear(embed_dim // 2, out_dim),
        )

    def forward(self, features: torch.Tensor):
        # Input features are continuous values, not embedding indices
        # Ensure features are float type
        features = features.float()
        return self.linear_relu_stack(features)


class MLPSeparate(nn.Module):
    """
    MLP model with separate networks for power and area prediction.
    This follows the same approach as sklearn models where we train
    two independent models for better specialization.
    """
    def __init__(self, num_embeddings=147, embed_dim=128) -> None:
        super().__init__()
        self.embed_dim = embed_dim
        self.f = 29             # feature num

        self.activation = nn.ReLU()

        # Separate MLP for power prediction
        self.power_mlp = nn.Sequential(
            nn.Linear(self.f, embed_dim),
            self.activation,
            nn.Dropout(0.2),

            nn.Linear(embed_dim, embed_dim),
            self.activation,
            nn.Dropout(0.2),

            nn.Linear(embed_dim, embed_dim // 2),
            self.activation,

            nn.Linear(embed_dim // 2, 1),  # Single output for power
        )

        # Separate MLP for area prediction
        self.area_mlp = nn.Sequential(
            nn.Linear(self.f, embed_dim),
            self.activation,
            nn.Dropout(0.2),

            nn.Linear(embed_dim, embed_dim),
            self.activation,
            nn.Dropout(0.2),

            nn.Linear(embed_dim, embed_dim // 2),
            self.activation,

            nn.Linear(embed_dim // 2, 1),  # Single output for area
        )

    def forward(self, features: torch.Tensor):
        # Input features are continuous values, not embedding indices
        # Ensure features are float type
        features = features.float()

        # Get predictions from both networks
        power_pred = self.power_mlp(features)
        area_pred = self.area_mlp(features)

        # Concatenate predictions to match expected output format [power, area]
        return torch.cat([power_pred, area_pred], dim=1)


def double_conv(in_channels, out_channels):
    return nn.Sequential(
        nn.Conv1d(in_channels, out_channels, 3, padding=1),
        nn.ReLU(inplace=True),
        nn.Conv1d(out_channels, out_channels, 3, padding=1),
        nn.ReLU(inplace=True)
    )

class UNet(nn.Module):

    def __init__(self, n_class=2, num_embeddings=147, embed_dim=64):
        super().__init__()

        self.embed_dim = embed_dim
        self.inter_dim = int(math.sqrt(embed_dim))

        # Remove embedding table and use direct convolution on features
        self.dconv_down1 = double_conv(1, 64)  # 1 input channel for continuous features
        self.dconv_down2 = double_conv(64, 128)
        self.dconv_down3 = double_conv(128, 256)
        self.dconv_down4 = double_conv(256, 512)

        self.maxpool = nn.MaxPool1d(2)
        self.upsample = nn.Upsample(scale_factor=2, mode='linear', align_corners=True)

        self.dconv_up3 = double_conv(256 + 512, 256)
        self.dconv_up2 = double_conv(128 + 256, 128)
        self.dconv_up1 = double_conv(128 + 64, 64)

        # Calculate the actual output size by doing a forward pass with dummy input
        self._calculate_fc_input_size()

        self.fc2 = nn.Linear(in_features=self.embed_dim, out_features=n_class)

    def _calculate_fc_input_size(self):
        """Calculate the input size for fc1 layer by doing a forward pass with dummy input."""
        # Create dummy input with the expected shape (batch_size=1, features=29)
        dummy_input = torch.randn(1, 29)

        # Run through the convolutional layers
        x = dummy_input.unsqueeze(1)  # (1, 1, 29)

        conv1 = self.dconv_down1(x)
        x = self.maxpool(conv1)

        conv2 = self.dconv_down2(x)
        x = self.maxpool(conv2)

        conv3 = self.dconv_down3(x)
        x = self.maxpool(conv3)

        x = self.dconv_down4(x)

        # Upsampling with dimension matching
        x = self.upsample(x)
        if x.size(-1) != conv3.size(-1):
            x = nn.functional.interpolate(x, size=conv3.size(-1), mode='linear', align_corners=True)
        x = torch.cat([x, conv3], dim=1)

        x = self.dconv_up3(x)
        x = self.upsample(x)
        if x.size(-1) != conv2.size(-1):
            x = nn.functional.interpolate(x, size=conv2.size(-1), mode='linear', align_corners=True)
        x = torch.cat([x, conv2], dim=1)

        x = self.dconv_up2(x)
        x = self.upsample(x)
        if x.size(-1) != conv1.size(-1):
            x = nn.functional.interpolate(x, size=conv1.size(-1), mode='linear', align_corners=True)
        x = torch.cat([x, conv1], dim=1)

        x = self.dconv_up1(x)

        # Calculate flattened size
        flattened_size = x.flatten(start_dim=1, end_dim=-1).size(1)

        # Create fc1 layer with correct input size
        self.fc1 = nn.Linear(in_features=flattened_size, out_features=self.embed_dim)

    def forward(self, x):
        # Convert continuous features to appropriate format for conv layers
        x = x.float()  # Ensure float type
        # x shape: (batch_size, 29) -> need (batch_size, 29, 1) for conv1d
        x = x.unsqueeze(1)  # (batch_size, 1, 29) - add channel dimension

        conv1 = self.dconv_down1(x)
        x = self.maxpool(conv1)

        conv2 = self.dconv_down2(x)
        x = self.maxpool(conv2)

        conv3 = self.dconv_down3(x)
        x = self.maxpool(conv3)

        x = self.dconv_down4(x)

        # Upsampling with dimension matching
        x = self.upsample(x)
        # Ensure spatial dimensions match for concatenation
        if x.size(-1) != conv3.size(-1):
            # Use adaptive interpolation to match conv3's spatial dimension
            x = nn.functional.interpolate(x, size=conv3.size(-1), mode='linear', align_corners=True)
        x = torch.cat([x, conv3], dim=1)

        x = self.dconv_up3(x)
        x = self.upsample(x)
        # Ensure spatial dimensions match for concatenation
        if x.size(-1) != conv2.size(-1):
            x = nn.functional.interpolate(x, size=conv2.size(-1), mode='linear', align_corners=True)
        x = torch.cat([x, conv2], dim=1)

        x = self.dconv_up2(x)
        x = self.upsample(x)
        # Ensure spatial dimensions match for concatenation
        if x.size(-1) != conv1.size(-1):
            x = nn.functional.interpolate(x, size=conv1.size(-1), mode='linear', align_corners=True)
        x = torch.cat([x, conv1], dim=1)

        x = self.dconv_up1(x)

        out = self.fc1(x.flatten(start_dim=1, end_dim=-1))
        out = self.fc2(nn.ReLU()(out))
        return out


class TransformerModel(nn.Module):
    def __init__(self, n_class=2, hidden_size=64, nhead=4, num_layers=4, num_embeddings=147):
        super(TransformerModel, self).__init__()
        self.feature_num = 29
        self.hidden_size = hidden_size
        self.transformer = nn.Transformer(hidden_size, nhead, num_layers, batch_first=True)
        self.fc = nn.Linear(hidden_size * self.feature_num, n_class)

        # Add input projection for continuous features
        self.input_projection = nn.Linear(1, hidden_size)

    def forward(self, x):
        # Convert continuous features to appropriate format for transformer
        x = x.float()  # Ensure float type
        # x shape: (batch_size, 29) -> need (batch_size, 29, hidden_size) for transformer
        x = x.unsqueeze(-1)  # (batch_size, 29, 1)
        x = self.input_projection(x)  # (batch_size, 29, hidden_size)

        x = self.transformer(x, x)
        out = self.fc(x.flatten(start_dim=1, end_dim=-1))
        return out