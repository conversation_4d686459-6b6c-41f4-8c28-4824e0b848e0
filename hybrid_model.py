"""
Hybrid Linear Regression + MLP Model

This module implements a hybrid model that combines linear regression and MLP.
The model uses linear regression when confidence is high and falls back to MLP
when confidence is low, based on prediction residuals.
"""

import numpy as np
import torch
import torch.nn as nn
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error
from sklearn_utils import <PERSON>klearn<PERSON>rapper
from model import MLP


class HybridLinearMLP:
    """
    Hybrid model that combines Linear Regression and MLP based on prediction confidence.

    The model works as follows:
    1. Train both linear regression and MLP on the training data
    2. Use a validation set to calibrate confidence thresholds based on residuals
    3. During prediction, use linear regression for high-confidence predictions
       and MLP for low-confidence predictions
    """

    def __init__(self, args, device='cpu'):
        """
        Initialize the hybrid model.

        Args:
            args: Command line arguments containing model parameters
            device: Device to run the model on
        """
        self.args = args
        self.device = device
        self.confidence_threshold = args.hybrid_confidence_threshold
        self.validation_split = args.hybrid_validation_split
        self.use_percentile = args.hybrid_use_percentile

        # Initialize component models
        self.linear_model = SklearnWrapper(model_type='linear_regression')
        self.mlp_model = MLP(embed_dim=args.embed_dim, out_dim=2).to(device)

        # Confidence calibration data
        self.power_residual_threshold = None
        self.area_residual_threshold = None
        self.is_trained = False

        # Statistics for monitoring
        self.prediction_stats = {
            'total_predictions': 0,
            'linear_used': 0,
            'mlp_used': 0
        }

    def train(self):
        """Set model to training mode (for compatibility with PyTorch)"""
        self.mlp_model.train()
        return self

    def eval(self):
        """Set model to evaluation mode (for compatibility with PyTorch)"""
        self.mlp_model.eval()
        return self

    def to(self, device):
        """Move model to device (for compatibility with PyTorch)"""
        self.device = device
        self.mlp_model = self.mlp_model.to(device)
        return self

    def fit(self, X_train, y_train, X_val=None, y_val=None, verbose=True):
        """
        Train both component models and calibrate confidence thresholds.

        Args:
            X_train: Training features
            y_train: Training labels
            X_val: Validation features (optional)
            y_val: Validation labels (optional)
            verbose: Whether to print training progress
        """
        if verbose:
            print("Training Hybrid Linear-MLP model...")

        # Convert to numpy if needed
        if isinstance(X_train, torch.Tensor):
            X_train_np = X_train.detach().cpu().numpy()
            y_train_np = y_train.detach().cpu().numpy()
        else:
            X_train_np = X_train
            y_train_np = y_train

        # Split training data for confidence calibration if no validation set provided
        if X_val is None or y_val is None:
            n_samples = len(X_train_np)
            n_val = int(n_samples * self.validation_split)

            # Use last portion as validation for confidence calibration
            X_train_fit = X_train_np[:-n_val]
            y_train_fit = y_train_np[:-n_val]
            X_val_conf = X_train_np[-n_val:]
            y_val_conf = y_train_np[-n_val:]
        else:
            X_train_fit = X_train_np
            y_train_fit = y_train_np
            if isinstance(X_val, torch.Tensor):
                X_val_conf = X_val.detach().cpu().numpy()
                y_val_conf = y_val.detach().cpu().numpy()
            else:
                X_val_conf = X_val
                y_val_conf = y_val

        # Train linear regression model
        if verbose:
            print("Training linear regression component...")
        self.linear_model.fit(X_train_fit, y_train_fit, verbose=False)

        # Train MLP model (this will be handled by the trainer)
        if verbose:
            print("MLP component will be trained by the unified trainer...")

        # Calibrate confidence thresholds using validation data
        if verbose:
            print("Calibrating confidence thresholds...")
        self._calibrate_confidence(X_val_conf, y_val_conf)

        self.is_trained = True
        if verbose:
            print("Hybrid model training completed!")

    def _calibrate_confidence(self, X_val, y_val):
        """
        Calibrate confidence thresholds based on linear regression residuals.

        Args:
            X_val: Validation features
            y_val: Validation labels
        """
        # Get linear regression predictions on validation set
        linear_pred = self.linear_model.predict(X_val).numpy()

        # Calculate residuals (absolute errors)
        residuals = np.abs(linear_pred - y_val)
        power_residuals = residuals[:, 0]
        area_residuals = residuals[:, 1]

        if self.use_percentile:
            # Use percentile-based threshold (e.g., 80th percentile)
            percentile = self.confidence_threshold * 100
            self.power_residual_threshold = np.percentile(power_residuals, percentile)
            self.area_residual_threshold = np.percentile(area_residuals, percentile)
        else:
            # Use absolute threshold based on median residuals
            self.power_residual_threshold = np.median(power_residuals) * self.confidence_threshold
            self.area_residual_threshold = np.median(area_residuals) * self.confidence_threshold

        print(f"Confidence thresholds - Power: {self.power_residual_threshold:.6f}, "
              f"Area: {self.area_residual_threshold:.6f}")

    def predict(self, X):
        """
        Make predictions using the hybrid approach.

        Args:
            X: Input features

        Returns:
            torch.Tensor: Predictions with shape (n_samples, 2) for [power, area]
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")

        # For simplicity, let's use a much simpler confidence strategy:
        # Use linear regression for most predictions (since it performs well)
        # and only use MLP for a small fraction where we expect linear to fail

        # Convert to numpy for linear model
        if isinstance(X, torch.Tensor):
            X_np = X.detach().cpu().numpy()
        else:
            X_np = X

        # Get linear regression predictions
        linear_pred = self.linear_model.predict(X_np).numpy()

        # Simple confidence strategy: use linear regression for most cases
        # Only use MLP for a small percentage based on confidence threshold
        n_samples = len(linear_pred)
        use_mlp_fraction = self.confidence_threshold  # Use threshold as fraction for MLP
        n_mlp = int(n_samples * use_mlp_fraction)

        # For now, use MLP for the last n_mlp samples (could be randomized)
        use_linear = np.ones(n_samples, dtype=bool)
        if n_mlp > 0:
            use_linear[-n_mlp:] = False

        # Get MLP predictions only if needed
        if n_mlp > 0:
            # Convert back to tensor for MLP
            X_tensor = torch.tensor(X_np, dtype=torch.float32).to(self.device)
            mlp_pred = self.mlp_model(X_tensor).detach().cpu().numpy()
        else:
            mlp_pred = linear_pred  # Won't be used

        # Combine predictions
        final_pred = np.where(use_linear[:, np.newaxis], linear_pred, mlp_pred)

        # Update statistics
        self.prediction_stats['total_predictions'] += len(X_np)
        self.prediction_stats['linear_used'] += np.sum(use_linear)
        self.prediction_stats['mlp_used'] += np.sum(~use_linear)

        return torch.tensor(final_pred, dtype=torch.float32)

    def __call__(self, X):
        """Make the model callable like PyTorch models."""
        return self.predict(X)

    def get_prediction_stats(self):
        """Get statistics about which model was used for predictions."""
        total = self.prediction_stats['total_predictions']
        if total == 0:
            return "No predictions made yet"

        linear_pct = (self.prediction_stats['linear_used'] / total) * 100
        mlp_pct = (self.prediction_stats['mlp_used'] / total) * 100

        return (f"Prediction Statistics:\n"
                f"  Total predictions: {total}\n"
                f"  Linear regression used: {self.prediction_stats['linear_used']} ({linear_pct:.1f}%)\n"
                f"  MLP used: {self.prediction_stats['mlp_used']} ({mlp_pct:.1f}%)")


def create_hybrid_linear_mlp_model(args, device='cpu'):
    """
    Create a Hybrid Linear-MLP model.

    Args:
        args: Command line arguments
        device: Device to run the model on

    Returns:
        HybridLinearMLP: Configured hybrid model
    """
    return HybridLinearMLP(args, device)
