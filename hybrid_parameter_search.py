"""
Hybrid Model Parameter Search

This script performs a systematic search for optimal hybrid model parameters
to find the best configuration for combining linear regression and MLP.
"""

import sys
import numpy as np
import pandas as pd
from arguments import parse_args, set_args
from main_refactored import setup_device, set_random_seeds
from data_processing import DataProcessor
from model_factory import ModelFactory
from trainer import UnifiedTrainer
from evaluator import ModelEvaluator
import itertools


def search_hybrid_parameters():
    """
    Perform grid search over hybrid model parameters to find optimal settings.
    """
    print("="*80)
    print("HYBRID MODEL PARAMETER SEARCH")
    print("="*80)

    # Define parameter search space
    search_params = {
        'hybrid_confidence_threshold': [0.05, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9],
        'hybrid_mlp_epochs': [40, 50, 60],  # Use 40+ epochs for better MLP training
        'hybrid_use_percentile': [False, True],
        'lr': [1e-4, 1e-3, 1e-2],  # Learning rate for <PERSON><PERSON>
    }

    # Base arguments
    base_args = parse_args()
    base_args.model_name = 'hybrid_linear_mlp'
    base_args.test_fold = 0  # Use single fold for faster search
    base_args.epochs = 40  # Set base epochs to 40

    results = []
    total_combinations = np.prod([len(v) for v in search_params.values()])

    print(f"Testing {total_combinations} parameter combinations...")
    print(f"Parameters to search: {list(search_params.keys())}")
    print()

    # Generate all parameter combinations
    param_names = list(search_params.keys())
    param_values = list(search_params.values())

    for i, combination in enumerate(itertools.product(*param_values)):
        print(f"Combination {i+1}/{total_combinations}")
        print("-" * 50)

        # Create args for this combination
        args = parse_args()
        args.model_name = 'hybrid_linear_mlp'
        args.test_fold = 0
        args.epochs = 10

        # Set search parameters
        for param_name, param_value in zip(param_names, combination):
            setattr(args, param_name, param_value)

        # Print current parameters
        param_str = ", ".join([f"{name}={value}" for name, value in zip(param_names, combination)])
        print(f"Parameters: {param_str}")

        try:
            # Run training with these parameters
            metrics = train_single_configuration(args)

            # Store results
            result = {
                'combination_id': i + 1,
                'overall_r2': metrics['overall_r2'],
                'overall_mse': metrics['overall_mse'],
                'power_accuracy': metrics['power_accuracy'],
                'area_accuracy': metrics['area_accuracy'],
                'power_r2': metrics['power_r2'],
                'area_r2': metrics['area_r2'],
            }

            # Add parameter values to result
            for param_name, param_value in zip(param_names, combination):
                result[param_name] = param_value

            results.append(result)

            print(f"Results: R²={metrics['overall_r2']:.4f}, MSE={metrics['overall_mse']:.2e}, "
                  f"Power_Acc={metrics['power_accuracy']:.4f}, Area_Acc={metrics['area_accuracy']:.4f}")

        except Exception as e:
            print(f"Error with combination {i+1}: {str(e)}")
            # Store failed result
            result = {
                'combination_id': i + 1,
                'overall_r2': -999,  # Mark as failed
                'overall_mse': float('inf'),
                'power_accuracy': 0,
                'area_accuracy': 0,
                'power_r2': -999,
                'area_r2': -999,
                'error': str(e)
            }
            for param_name, param_value in zip(param_names, combination):
                result[param_name] = param_value
            results.append(result)

        print()

    # Analyze results
    analyze_results(results, param_names)

    return results


def train_single_configuration(args):
    """
    Train hybrid model with given configuration and return metrics.

    Args:
        args: Arguments with hybrid parameters set

    Returns:
        dict: Evaluation metrics
    """
    # Set random seeds for reproducibility
    set_random_seeds(args.random_seed)
    set_args(args)

    # Setup device
    device = setup_device(args)

    # Data processing
    apply_label_scaling = ModelFactory.is_pytorch_model(args.model_name)
    data_processor = DataProcessor(
        apply_feature_engineering=args.apply_feature_engineering,
        apply_feature_scaling=args.apply_scaling,
        apply_label_scaling=apply_label_scaling
    )

    # Get data in numpy format for hybrid model
    X_train, y_train, X_test, y_test = data_processor.process_data(args, return_format='numpy')
    train_data = (X_train, y_train)
    test_data = (X_test, y_test)

    # Create and train model
    model = ModelFactory.create_model(args.model_name, args, device)
    trainer = UnifiedTrainer(model, args.model_name, args, device)

    # Train model (suppress output for cleaner search results)
    import io
    import contextlib

    # Capture stdout to reduce noise during search
    f = io.StringIO()
    with contextlib.redirect_stdout(f):
        _, _, _, enhanced_metrics = trainer.train(train_data, test_data)

    return enhanced_metrics


def analyze_results(results, param_names):
    """
    Analyze parameter search results and find optimal configurations.

    Args:
        results: List of result dictionaries
        param_names: List of parameter names that were searched
    """
    print("="*80)
    print("PARAMETER SEARCH ANALYSIS")
    print("="*80)

    # Convert to DataFrame for easier analysis
    df = pd.DataFrame(results)

    # Filter out failed runs
    valid_df = df[df['overall_r2'] > -999].copy()

    if len(valid_df) == 0:
        print("No valid results found!")
        return

    print(f"Valid results: {len(valid_df)}/{len(df)}")
    print()

    # Find best configurations for different metrics
    metrics_to_optimize = [
        ('overall_r2', 'max', 'Overall R²'),
        ('overall_mse', 'min', 'Overall MSE'),
        ('power_accuracy', 'max', 'Power Accuracy'),
        ('area_accuracy', 'max', 'Area Accuracy')
    ]

    print("BEST CONFIGURATIONS:")
    print("="*50)

    for metric, direction, description in metrics_to_optimize:
        if direction == 'max':
            best_idx = valid_df[metric].idxmax()
        else:
            best_idx = valid_df[metric].idxmin()

        best_row = valid_df.loc[best_idx]

        print(f"\nBest {description}: {best_row[metric]:.6f}")
        print("Parameters:")
        for param in param_names:
            print(f"  --{param.replace('_', '-')} {best_row[param]}")
        print(f"  Overall R²: {best_row['overall_r2']:.6f}")
        print(f"  Overall MSE: {best_row['overall_mse']:.2e}")
        print(f"  Power Accuracy: {best_row['power_accuracy']:.4f}")
        print(f"  Area Accuracy: {best_row['area_accuracy']:.4f}")

    # Parameter impact analysis
    print("\n" + "="*50)
    print("PARAMETER IMPACT ANALYSIS:")
    print("="*50)

    for param in param_names:
        print(f"\n{param}:")
        param_analysis = valid_df.groupby(param)['overall_r2'].agg(['mean', 'std', 'count'])
        for value, stats in param_analysis.iterrows():
            print(f"  {value}: R²={stats['mean']:.4f} ±{stats['std']:.4f} (n={stats['count']})")

    # Save detailed results
    output_file = 'hybrid_parameter_search_results.csv'
    df.to_csv(output_file, index=False)
    print(f"\nDetailed results saved to: {output_file}")

    # Save summary of best configurations
    summary_file = 'hybrid_best_configurations.txt'
    with open(summary_file, 'w') as f:
        f.write("HYBRID MODEL PARAMETER SEARCH - BEST CONFIGURATIONS\n")
        f.write("="*60 + "\n\n")

        for metric, direction, description in metrics_to_optimize:
            if direction == 'max':
                best_idx = valid_df[metric].idxmax()
            else:
                best_idx = valid_df[metric].idxmin()

            best_row = valid_df.loc[best_idx]

            f.write(f"Best {description}: {best_row[metric]:.6f}\n")
            f.write("Command to reproduce:\n")
            cmd_parts = ["python3 main_refactored.py --model-name hybrid_linear_mlp"]
            for param in param_names:
                cmd_parts.append(f"--{param.replace('_', '-')} {best_row[param]}")
            f.write(" ".join(cmd_parts) + "\n\n")

    print(f"Best configurations saved to: {summary_file}")


if __name__ == '__main__':
    results = search_hybrid_parameters()
