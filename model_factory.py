"""
Unified model factory for creating all supported model types.
Provides consistent interface for model creation and configuration.
"""

import torch
import torch.nn as nn
from model import M<PERSON>, MLPSeparate, UNet, TransformerModel
from xgboost_utils import XGBoostWrapper
from sklearn_utils import <PERSON>klearnWrapper
from sklearn.ensemble import RandomForestRegressor
from sklearn.neighbors import KNeighborsRegressor
from sklearn.svm import SVR
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import RB<PERSON>, Matern, RationalQuadratic
from sklearn.linear_model import Ridge
import xgboost as xgb

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False


class ModelFactory:
    """Factory class for creating different types of models."""

    PYTORCH_MODELS = ['mlp', 'mlp_separate', 'unet', 'transformer', 'cnn', 'resnet', 'tabnet']
    SKLEARN_MODELS = ['linear_regression', 'ridge', 'gaussian_process', 'knn', 'svr', 'random_forest']
    XGBOOST_MODELS = ['xgboost']
    LIGHTGBM_MODELS = ['lightgbm'] if LIGHTGBM_AVAILABLE else []

    ALL_MODELS = PYTORCH_MODELS + SKLEARN_MODELS + XGBOOST_MODELS + LIGHTGBM_MODELS

    @staticmethod
    def get_supported_models():
        """Return list of all supported model names."""
        return ModelFactory.ALL_MODELS

    @staticmethod
    def is_pytorch_model(model_name):
        """Check if model is a PyTorch model."""
        return model_name in ModelFactory.PYTORCH_MODELS

    @staticmethod
    def is_sklearn_model(model_name):
        """Check if model is a scikit-learn model."""
        return model_name in ModelFactory.SKLEARN_MODELS

    @staticmethod
    def is_xgboost_model(model_name):
        """Check if model is an XGBoost model."""
        return model_name in ModelFactory.XGBOOST_MODELS

    @staticmethod
    def is_lightgbm_model(model_name):
        """Check if model is a LightGBM model."""
        return model_name in ModelFactory.LIGHTGBM_MODELS

    @staticmethod
    def create_pytorch_model(model_name, args, device='cpu'):
        """Create PyTorch model."""
        if model_name == 'mlp':
            model = MLP(embed_dim=args.embed_dim)
        elif model_name == 'mlp_separate':
            model = MLPSeparate(embed_dim=args.embed_dim)
        elif model_name == 'unet':
            model = UNet(embed_dim=args.embed_dim)
        elif model_name == 'transformer':
            model = TransformerModel(
                hidden_size=args.embed_dim,
                nhead=args.nheads,
                num_layers=args.nlayers
            )
        elif model_name == 'cnn':
            # Simple CNN implementation
            model = SimpleCNN(embed_dim=args.embed_dim)
        elif model_name == 'resnet':
            # ResNet implementation
            model = SimpleResNet(embed_dim=args.embed_dim)
        elif model_name == 'tabnet':
            # TabNet implementation (placeholder)
            raise NotImplementedError("TabNet not yet implemented")
        else:
            raise ValueError(f"Unknown PyTorch model: {model_name}")

        return model.to(device)

    @staticmethod
    def create_sklearn_model(model_name, args):
        """Create scikit-learn model."""
        if model_name == 'linear_regression':
            return SklearnWrapper(model_type='linear_regression')

        elif model_name == 'ridge':
            model_params = {
                'alpha': getattr(args, 'ridge_alpha', 1.0),
                'random_state': 42
            }
            return SklearnWrapper(model_type='ridge', **model_params)

        elif model_name == 'gaussian_process':
            # Create kernel based on args
            kernel_type = getattr(args, 'gp_kernel', 'rbf')
            if kernel_type == 'rbf':
                kernel = RBF(length_scale=1.0)
            elif kernel_type == 'matern':
                kernel = Matern(length_scale=1.0, nu=1.5)
            elif kernel_type == 'rational_quadratic':
                kernel = RationalQuadratic(length_scale=1.0, alpha=1.0)
            else:
                kernel = RBF(length_scale=1.0)

            model_params = {
                'kernel': kernel,
                'alpha': getattr(args, 'gp_alpha', 1e-10),
                'normalize_y': getattr(args, 'gp_normalize_y', False),
                'copy_X_train': True,
                'random_state': 42
            }
            return SklearnWrapper(model_type='gaussian_process', **model_params)

        elif model_name == 'knn':
            model_params = {
                'n_neighbors': getattr(args, 'knn_neighbors', 5),
                'weights': getattr(args, 'knn_weights', 'uniform'),
                'algorithm': getattr(args, 'knn_algorithm', 'auto')
            }
            return SklearnWrapper(model_type='knn', **model_params)

        elif model_name == 'svr':
            model_params = {
                'kernel': getattr(args, 'svr_kernel', 'rbf'),
                'C': getattr(args, 'svr_C', 1.0),
                'gamma': getattr(args, 'svr_gamma', 'scale'),
                'epsilon': getattr(args, 'svr_epsilon', 0.1)
            }
            return SklearnWrapper(model_type='svr', **model_params)

        elif model_name == 'random_forest':
            model_params = {
                'n_estimators': getattr(args, 'rf_n_estimators', 100),
                'max_depth': getattr(args, 'rf_max_depth', None),
                'min_samples_split': getattr(args, 'rf_min_samples_split', 2),
                'min_samples_leaf': getattr(args, 'rf_min_samples_leaf', 1),
                'random_state': 42
            }
            return SklearnWrapper(model_type='random_forest', **model_params)

        else:
            raise ValueError(f"Unknown sklearn model: {model_name}")

    @staticmethod
    def create_xgboost_model(model_name, args):
        """Create XGBoost model."""
        if model_name == 'xgboost':
            xgb_params = {
                'objective': 'reg:squarederror',
                'n_estimators': args.xgb_n_estimators,
                'max_depth': args.xgb_max_depth,
                'learning_rate': args.xgb_learning_rate,
                'subsample': args.xgb_subsample,
                'colsample_bytree': args.xgb_colsample_bytree,
                'reg_alpha': args.xgb_reg_alpha,
                'reg_lambda': args.xgb_reg_lambda,
                'random_state': 42,
                'verbosity': 1
            }
            return XGBoostWrapper(**xgb_params)
        else:
            raise ValueError(f"Unknown XGBoost model: {model_name}")

    @staticmethod
    def create_lightgbm_model(model_name, args):
        """Create LightGBM model."""
        if not LIGHTGBM_AVAILABLE:
            raise ImportError("LightGBM is not installed")

        if model_name == 'lightgbm':
            # LightGBM wrapper implementation would go here
            raise NotImplementedError("LightGBM wrapper not yet implemented")
        else:
            raise ValueError(f"Unknown LightGBM model: {model_name}")

    @staticmethod
    def create_model(model_name, args, device='cpu'):
        """
        Create model of specified type.

        Args:
            model_name: Name of the model to create
            args: Command line arguments
            device: Device for PyTorch models

        Returns:
            Created model instance
        """
        if model_name not in ModelFactory.ALL_MODELS:
            raise ValueError(f"Unsupported model: {model_name}. "
                           f"Supported models: {ModelFactory.ALL_MODELS}")

        if ModelFactory.is_pytorch_model(model_name):
            return ModelFactory.create_pytorch_model(model_name, args, device)
        elif ModelFactory.is_sklearn_model(model_name):
            return ModelFactory.create_sklearn_model(model_name, args)
        elif ModelFactory.is_xgboost_model(model_name):
            return ModelFactory.create_xgboost_model(model_name, args)
        elif ModelFactory.is_lightgbm_model(model_name):
            return ModelFactory.create_lightgbm_model(model_name, args)
        else:
            raise ValueError(f"Unknown model category for: {model_name}")


# Simple CNN implementation for missing models
class SimpleCNN(nn.Module):
    def __init__(self, embed_dim=64, out_dim=2):
        super().__init__()
        self.embed_dim = embed_dim
        self.feature_num = 29

        self.conv1 = nn.Conv1d(1, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv1d(32, 64, kernel_size=3, padding=1)
        self.conv3 = nn.Conv1d(64, 128, kernel_size=3, padding=1)

        self.pool = nn.MaxPool1d(2)
        self.dropout = nn.Dropout(0.2)

        # Calculate the size after convolutions
        conv_output_size = 128 * (self.feature_num // 8)  # After 3 pooling operations

        self.fc1 = nn.Linear(conv_output_size, embed_dim)
        self.fc2 = nn.Linear(embed_dim, out_dim)
        self.relu = nn.ReLU()

    def forward(self, x):
        # x shape: (batch_size, 29)
        x = x.unsqueeze(1)  # Add channel dimension: (batch_size, 1, 29)

        x = self.relu(self.conv1(x))
        x = self.pool(x)
        x = self.dropout(x)

        x = self.relu(self.conv2(x))
        x = self.pool(x)
        x = self.dropout(x)

        x = self.relu(self.conv3(x))
        x = self.pool(x)
        x = self.dropout(x)

        x = x.flatten(1)  # Flatten for fully connected layers
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.fc2(x)

        return x


# Simple ResNet implementation
class SimpleResNet(nn.Module):
    def __init__(self, embed_dim=64, out_dim=2):
        super().__init__()
        self.embed_dim = embed_dim
        self.feature_num = 29

        # Initial projection
        self.input_proj = nn.Linear(self.feature_num, embed_dim)

        # Residual blocks
        self.res_block1 = ResidualBlock(embed_dim)
        self.res_block2 = ResidualBlock(embed_dim)
        self.res_block3 = ResidualBlock(embed_dim)

        # Output layer
        self.output = nn.Linear(embed_dim, out_dim)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.2)

    def forward(self, x):
        x = self.relu(self.input_proj(x))
        x = self.res_block1(x)
        x = self.res_block2(x)
        x = self.res_block3(x)
        x = self.dropout(x)
        x = self.output(x)
        return x


class ResidualBlock(nn.Module):
    def __init__(self, dim):
        super().__init__()
        self.linear1 = nn.Linear(dim, dim)
        self.linear2 = nn.Linear(dim, dim)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.1)

    def forward(self, x):
        residual = x
        x = self.relu(self.linear1(x))
        x = self.dropout(x)
        x = self.linear2(x)
        x = x + residual  # Residual connection
        x = self.relu(x)
        return x
