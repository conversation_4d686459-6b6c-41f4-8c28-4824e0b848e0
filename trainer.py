"""
Unified trainer module for all model types.
Provides consistent training interface across different models.
"""

import torch
import torch.nn as nn
import numpy as np
from evaluator import ModelEvaluator
from model_factory import ModelFactory


class UnifiedTrainer:
    """
    Unified trainer that can handle PyTorch, sklearn, and XGBoost models.
    """

    def __init__(self, model, model_name, args, device='cpu'):
        self.model = model
        self.model_name = model_name
        self.args = args
        self.device = device
        self.evaluator = ModelEvaluator()

    def train_pytorch_model(self, train_dataloader, test_dataloader):
        """Train PyTorch model."""
        # Clear CUDA cache before training
        if self.device.startswith('cuda'):
            torch.cuda.empty_cache()

        # Setup loss function and optimizer
        loss_fn = nn.MSELoss()
        optimizer = torch.optim.AdamW(
            self.model.parameters(),
            lr=self.args.lr,
            weight_decay=self.args.weight_decay
        )
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer, T_max=self.args.epochs
        )

        # Training history
        train_losses = []
        test_losses = []
        test_acc = [[], []]  # [power_acc, area_acc]

        for epoch in range(self.args.epochs):
            print(f"Epoch {epoch+1}/{self.args.epochs}")
            print("-" * 30)

            # Training phase
            train_loss = self._train_epoch(train_dataloader, loss_fn, optimizer, scheduler)
            train_losses.append(train_loss)

            # Evaluation phase
            test_metrics = self.evaluator.evaluate_pytorch_model(
                test_dataloader, self.model, loss_fn, self.device
            )
            test_losses.append(test_metrics['test_loss'])
            test_acc[0].append(test_metrics['power_accuracy'])
            test_acc[1].append(test_metrics['area_accuracy'])

            print(f"Train Loss: {train_loss:.6f}, Test Loss: {test_metrics['test_loss']:.6f}")
            print(f"Power Acc: {test_metrics['power_accuracy']:.4f}, Area Acc: {test_metrics['area_accuracy']:.4f}")

            # Clear CUDA cache periodically to prevent memory accumulation
            if self.device.startswith('cuda') and (epoch + 1) % 10 == 0:
                torch.cuda.empty_cache()

        # Final evaluation
        final_metrics = self.evaluator.evaluate_pytorch_model(
            test_dataloader, self.model, loss_fn, self.device
        )

        return train_losses, test_losses, test_acc, final_metrics

    def _train_epoch(self, dataloader, loss_fn, optimizer, scheduler):
        """Train one epoch for PyTorch model."""
        size = len(dataloader.dataset)
        self.model.train()
        total_loss = 0

        for batch_id, (embedding, label) in enumerate(dataloader):
            embedding, label = embedding.to(self.device), label.to(self.device)

            # Forward pass
            pred = self.model(embedding)
            loss = loss_fn(pred, label)

            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            total_loss += loss.item()

            if batch_id % 100 == 0:
                current = (batch_id + 1) * len(embedding)
                print(f"  Batch {batch_id}: loss: {loss.item():.6f} [{current:>5d}/{size:>5d}]")

            # Clear intermediate tensors to save memory
            del embedding, label, pred, loss

        if scheduler is not None:
            scheduler.step()

        return total_loss / len(dataloader)

    def train_sklearn_model(self, X_train, y_train, X_test, y_test):
        """Train sklearn model."""
        print(f"Training {self.model_name} model...")

        # Train the model
        self.model.fit(X_train, y_train, verbose=True)

        # Evaluate
        train_metrics = self.evaluator.evaluate_sklearn_model(X_train, y_train, self.model, self.args)
        test_metrics = self.evaluator.evaluate_sklearn_model(X_test, y_test, self.model, self.args)

        # Print results
        print(f"Train MSE: {train_metrics['overall_mse']:.6f}, Test MSE: {test_metrics['overall_mse']:.6f}")
        print(f"Train R²: {train_metrics['overall_r2']:.6f}, Test R²: {test_metrics['overall_r2']:.6f}")
        print(f"Power Accuracy: {test_metrics['power_accuracy']:.4f}, Area Accuracy: {test_metrics['area_accuracy']:.4f}")

        # Return in format compatible with plotting functions
        train_losses = [train_metrics['overall_mse']]
        test_losses = [test_metrics['overall_mse']]
        test_acc = [[test_metrics['power_accuracy']], [test_metrics['area_accuracy']]]

        return train_losses, test_losses, test_acc, test_metrics

    def train_xgboost_model(self, X_train, y_train, X_test, y_test):
        """Train XGBoost model."""
        print("Training XGBoost model...")

        # Train the model
        self.model.fit(X_train, y_train, X_test, y_test, verbose=True)

        # Evaluate
        train_metrics = self.evaluator.evaluate_sklearn_model(X_train, y_train, self.model, self.args)
        test_metrics = self.evaluator.evaluate_sklearn_model(X_test, y_test, self.model, self.args)

        # Print results
        print(f"Train MSE: {train_metrics['overall_mse']:.6f}, Test MSE: {test_metrics['overall_mse']:.6f}")
        print(f"Train R²: {train_metrics['overall_r2']:.6f}, Test R²: {test_metrics['overall_r2']:.6f}")
        print(f"Power Accuracy: {test_metrics['power_accuracy']:.4f}, Area Accuracy: {test_metrics['area_accuracy']:.4f}")

        # Return in format compatible with plotting functions
        train_losses = [train_metrics['overall_mse']]
        test_losses = [test_metrics['overall_mse']]
        test_acc = [[test_metrics['power_accuracy']], [test_metrics['area_accuracy']]]

        return train_losses, test_losses, test_acc, test_metrics

    def train_hybrid_model(self, X_train, y_train, X_test, y_test):
        """Train hybrid model."""
        print(f"Training {self.model_name} model...")

        # Train the hybrid model (this handles both linear regression and MLP training)
        self.model.fit(X_train, y_train, verbose=True)

        # For the MLP component, we need to train it using PyTorch
        # Create DataLoaders for MLP training
        from data_processing import create_dataloaders

        # Convert numpy arrays back to the format expected by create_dataloaders
        # We'll use the existing data processing pipeline
        train_dataloader, test_dataloader = create_dataloaders(self.args)

        # Train MLP component
        print("Training MLP component of hybrid model...")
        loss_fn = nn.MSELoss()
        optimizer = torch.optim.Adam(self.model.mlp_model.parameters(),
                                   lr=self.args.lr,
                                   weight_decay=self.args.weight_decay)

        train_losses = []
        test_losses = []
        test_acc = [[], []]

        # Use hybrid-specific epochs if available, otherwise use default epochs
        mlp_epochs = getattr(self.args, 'hybrid_mlp_epochs', self.args.epochs)
        for epoch in range(mlp_epochs):
            # Train MLP
            self.model.mlp_model.train()
            epoch_loss = 0
            num_batches = 0

            for embedding, label in train_dataloader:
                embedding, label = embedding.to(self.device), label.to(self.device)

                pred = self.model.mlp_model(embedding)
                loss = loss_fn(pred, label)

                optimizer.zero_grad()
                loss.backward()
                optimizer.step()

                epoch_loss += loss.item()
                num_batches += 1

            avg_train_loss = epoch_loss / num_batches
            train_losses.append(avg_train_loss)

            # Evaluate MLP
            self.model.mlp_model.eval()
            test_metrics = self.evaluator.evaluate_pytorch_model(test_dataloader, self.model.mlp_model, loss_fn, self.device)
            test_losses.append(test_metrics['test_loss'])
            test_acc[0].append(test_metrics['power_accuracy'])
            test_acc[1].append(test_metrics['area_accuracy'])

            if epoch % 10 == 0 or epoch == mlp_epochs - 1:
                print(f"Epoch {epoch}: Train Loss: {avg_train_loss:.6f}, Test Loss: {test_metrics['test_loss']:.6f}")

        # Final evaluation using the hybrid model
        final_metrics = self.evaluator.evaluate_sklearn_model(X_test, y_test, self.model, self.args)

        # Print hybrid model statistics
        print("\n" + self.model.get_prediction_stats())

        print(f"Final Hybrid Model - MSE: {final_metrics['overall_mse']:.6f}, R²: {final_metrics['overall_r2']:.6f}")
        print(f"Power Accuracy: {final_metrics['power_accuracy']:.4f}, Area Accuracy: {final_metrics['area_accuracy']:.4f}")

        return train_losses, test_losses, test_acc, final_metrics

    def train(self, train_data, test_data):
        """
        Main training method that automatically selects the appropriate training strategy.

        Args:
            train_data: Training data (DataLoader for PyTorch, (X, y) tuple for others)
            test_data: Test data (DataLoader for PyTorch, (X, y) tuple for others)

        Returns:
            tuple: (train_losses, test_losses, test_acc, final_metrics)
        """
        if ModelFactory.is_pytorch_model(self.model_name):
            return self.train_pytorch_model(train_data, test_data)
        elif ModelFactory.is_sklearn_model(self.model_name):
            X_train, y_train = train_data
            X_test, y_test = test_data
            return self.train_sklearn_model(X_train, y_train, X_test, y_test)
        elif ModelFactory.is_xgboost_model(self.model_name):
            X_train, y_train = train_data
            X_test, y_test = test_data
            return self.train_xgboost_model(X_train, y_train, X_test, y_test)
        elif ModelFactory.is_hybrid_model(self.model_name):
            X_train, y_train = train_data
            X_test, y_test = test_data
            return self.train_hybrid_model(X_train, y_train, X_test, y_test)
        else:
            raise ValueError(f"Unknown model type: {self.model_name}")


# Legacy functions for backward compatibility
def train(dataloader, model, loss_fn, optimizer, scheduler=None, device='cpu'):
    """Legacy training function for backward compatibility."""
    size = len(dataloader.dataset)
    model.train()
    total_loss = 0

    for batch_id, (embedding, label) in enumerate(dataloader):
        embedding, label = embedding.to(device), label.to(device)

        # Compute prediction error
        pred = model(embedding)
        loss = loss_fn(pred, label)

        # Backpropagation
        loss.backward()
        optimizer.step()
        optimizer.zero_grad()

        total_loss += loss.item()

        if scheduler is not None:
            scheduler.step()

        if batch_id % 1000 == 0:
            current = (batch_id + 1) * len(embedding)
            print(f"Training loss: {loss.item():>7f}, at epoch [{current:>5d}/{size:>5d}]")

    return total_loss / len(dataloader)
